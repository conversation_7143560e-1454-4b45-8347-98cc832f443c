<?php $__env->startSection('title', $tutorialData['title']); ?>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/tutorial-detail.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="progress-indicator">
    <div class="progress-bar" id="progressBar"></div>
</div>

<div class="tutorial-container">
    <div class="container">
        <!-- Header -->
        <div class="tutorial-header">
            <h1 class="tutorial-title"><?php echo e($tutorialData['title']); ?></h1>
            <p class="tutorial-subtitle"><?php echo e($tutorialData['subtitle']); ?></p>
            <p class="tutorial-description"><?php echo $tutorialData['description']; ?></p>
            
            <div class="text-center mt-4">
                <a href="<?php echo e(route('pengolahan-peta')); ?>" class="back-button">
                    <i class="fas fa-arrow-left"></i>
                    Kembali ke Pengolahan Peta
                </a>
            </div>
        </div>
        
        <!-- Tutorial Sections -->
        <?php $__currentLoopData = $tutorialData['sections']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="section-card" data-section="<?php echo e($index); ?>">
            <div class="section-header" onclick="toggleSection('<?php echo e($section['id']); ?>')">
                <h3 class="section-title">
                    <i class="<?php echo e($section['icon']); ?> section-icon"></i>
                    <?php echo e($section['title']); ?>

                    <i class="fas fa-chevron-down collapse-icon" id="icon-<?php echo e($section['id']); ?>"></i>
                </h3>
            </div>
            
            <div class="section-content" id="content-<?php echo e($section['id']); ?>">
                <?php $__currentLoopData = $section['steps']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $step): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="step-item">
                    <div class="step-number"><?php echo e($step['step']); ?></div>
                    <div class="step-content">
                        <h4 class="step-title"><?php echo e($step['title']); ?></h4>
                        <div class="step-description"><?php echo $step['description']; ?></div>
                        <?php if(isset($step['image'])): ?>
                        <div class="step-image mt-3">
                            <img src="<?php echo e(asset($step['image'])); ?>" alt="<?php echo e($step['title']); ?>" class="img-fluid rounded shadow-sm" style="max-width: 100%; height: auto;">
                        </div>
                        <?php endif; ?>
                        <?php if($step['note']): ?>
                        <div class="step-note"><?php echo $step['note']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        
        <!-- Footer -->
        <div class="text-center mt-4">
            <a href="<?php echo e(route('pengolahan-peta')); ?>" class="back-button">
                <i class="fas fa-arrow-left"></i>
                Kembali ke Pengolahan Peta
            </a>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/tutorial-detail.js')); ?>"></script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\pengolahanwilker\resources\views/training/editing-peta-digital-detail.blade.php ENDPATH**/ ?>