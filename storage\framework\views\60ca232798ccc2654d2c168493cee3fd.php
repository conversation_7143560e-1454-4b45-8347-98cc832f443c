<?php $__env->startSection('title', 'Kontak & Bantuan - Pelatihan Pengolahan Wilkerstat SE2026'); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<div class="page-header mb-8">
    <div class="page-header-content">
        <div class="page-title-section">
            <h1 class="page-title">
                <i class="fas fa-headset"></i>
                Kontak & Bantuan
            </h1>
            <p class="page-subtitle">
                Dapatkan dukungan teknis dan bantuan untuk Pelatihan Pengolahan Wilkerstat SE2026. <PERSON> kami siap membantu Anda.
            </p>
        </div>
        <div class="page-actions">
            <button class="btn btn-primary" onclick="openTicketModal()">
                <i class="fas fa-ticket-alt"></i>
                Buat Tiket Bantuan
            </button>
        </div>
    </div>
</div>

<!-- Quick Help Cards -->
<div class="quick-help-section mb-8">
    <div class="grid grid-cols-1 grid-cols-md-3">
        <div class="help-card">
            <div class="help-icon">
                <i class="fas fa-question-circle"></i>
            </div>
            <h3>FAQ</h3>
            <p>Temukan jawaban untuk pertanyaan yang sering diajukan</p>
            <button class="btn btn-outline" onclick="scrollToSection('faq')">
                <i class="fas fa-arrow-down"></i>
                Lihat FAQ
            </button>
        </div>
        
        <div class="help-card">
            <div class="help-icon">
                <i class="fas fa-video"></i>
            </div>
            <h3>Tutorial Video</h3>
            <p>Panduan video langkah demi langkah</p>
            <button class="btn btn-outline" onclick="scrollToSection('tutorials')">
                <i class="fas fa-play"></i>
                Tonton Tutorial
            </button>
        </div>
        
        <div class="help-card">
            <div class="help-icon">
                <i class="fas fa-download"></i>
            </div>
            <h3>Unduhan</h3>
            <p>Manual, template, dan file pendukung</p>
            <button class="btn btn-outline" onclick="scrollToSection('downloads')">
                <i class="fas fa-download"></i>
                Lihat Unduhan
            </button>
        </div>
    </div>
</div>

<!-- Contact Information -->
<div class="contact-section mb-8">
    <div class="grid grid-cols-1 grid-cols-lg-2">
        <!-- Contact Details -->
        <div class="contact-info">
            <h2 class="section-title">
                <i class="fas fa-address-book"></i>
                Informasi Kontak
            </h2>
            
            <div class="contact-methods">
                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="method-content">
                        <h4>Telepon</h4>
                        <p><?php echo e($contact['phone']); ?></p>
                        <span class="availability">Senin - Jumat, 08:00 - 16:00 WIB</span>
                    </div>
                    <div class="method-action">
                        <a href="tel:<?php echo e($contact['phone']); ?>" class="btn btn-sm btn-primary">
                            <i class="fas fa-phone"></i>
                            Hubungi
                        </a>
                    </div>
                </div>
                
                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="method-content">
                        <h4>Email</h4>
                        <p><?php echo e($contact['email']); ?></p>
                        <span class="availability">Respon dalam 24 jam</span>
                    </div>
                    <div class="method-action">
                        <a href="mailto:<?php echo e($contact['email']); ?>" class="btn btn-sm btn-primary">
                            <i class="fas fa-envelope"></i>
                            Kirim Email
                        </a>
                    </div>
                </div>
                
                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <div class="method-content">
                        <h4>WhatsApp</h4>
                        <p><?php echo e($contact['whatsapp']); ?></p>
                        <span class="availability">Senin - Jumat, 08:00 - 16:00 WIB</span>
                    </div>
                    <div class="method-action">
                        <a href="https://wa.me/<?php echo e(str_replace(['+', '-', ' '], '', $contact['whatsapp'])); ?>" class="btn btn-sm btn-success" target="_blank">
                            <i class="fab fa-whatsapp"></i>
                            Chat
                        </a>
                    </div>
                </div>
                
                <div class="contact-method">
                    <div class="method-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="method-content">
                        <h4>Alamat</h4>
                        <p><?php echo e($contact['address']); ?></p>
                        <span class="availability">Kunjungan dengan perjanjian</span>
                    </div>
                    <div class="method-action">
                        <button class="btn btn-sm btn-outline" onclick="showMap()">
                            <i class="fas fa-map"></i>
                            Lihat Peta
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Contact Form -->
        <div class="contact-form-section">
            <h2 class="section-title">
                <i class="fas fa-paper-plane"></i>
                Kirim Pesan
            </h2>
            
            <form class="contact-form" id="contactForm">
                <div class="form-group">
                    <label for="name">Nama Lengkap *</label>
                    <input type="text" id="name" name="name" class="form-input" required>
                </div>
                
                <div class="form-group">
                    <label for="email">Email *</label>
                    <input type="email" id="email" name="email" class="form-input" required>
                </div>
                
                <div class="form-group">
                    <label for="phone">Nomor Telepon</label>
                    <input type="tel" id="phone" name="phone" class="form-input">
                </div>
                
                <div class="form-group">
                    <label for="subject">Subjek *</label>
                    <select id="subject" name="subject" class="form-select" required>
                        <option value="">Pilih subjek...</option>
                        <option value="technical">Bantuan Teknis</option>
                        <option value="training">Pertanyaan Pelatihan</option>
                        <option value="material">Materi & Sumber Daya</option>
                        <option value="schedule">Jadwal Pelatihan</option>
                        <option value="other">Lainnya</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="priority">Prioritas</label>
                    <select id="priority" name="priority" class="form-select">
                        <option value="low">Rendah</option>
                        <option value="medium" selected>Sedang</option>
                        <option value="high">Tinggi</option>
                        <option value="urgent">Mendesak</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="message">Pesan *</label>
                    <textarea id="message" name="message" class="form-textarea" rows="5" placeholder="Jelaskan pertanyaan atau masalah Anda secara detail..." required></textarea>
                </div>
                
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="newsletter" name="newsletter">
                        <span class="checkmark"></span>
                        Saya ingin menerima update dan informasi terbaru
                    </label>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                        <i class="fas fa-undo"></i>
                        Reset
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i>
                        Kirim Pesan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- FAQ Section -->
<div class="faq-section mb-8" id="faq">
    <h2 class="section-title">
        <i class="fas fa-question-circle"></i>
        Pertanyaan yang Sering Diajukan (FAQ)
    </h2>
    
    <div class="faq-categories">
        <div class="faq-tabs">
            <button class="faq-tab active" onclick="showFaqCategory('general')">Umum</button>
            <button class="faq-tab" onclick="showFaqCategory('technical')">Teknis</button>
            <button class="faq-tab" onclick="showFaqCategory('training')">Pelatihan</button>
            <button class="faq-tab" onclick="showFaqCategory('materials')">Materi</button>
        </div>
        
        <div class="faq-search">
            <div class="search-input-group">
                <i class="fas fa-search search-icon"></i>
                <input type="text" id="faqSearch" class="search-input" placeholder="Cari FAQ...">
            </div>
        </div>
    </div>
    
    <div class="faq-content">
        <?php $__currentLoopData = $faqs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category => $categoryFaqs): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="faq-category-content" id="faq-<?php echo e($category); ?>" style="<?php echo e($category === 'general' ? 'display: block;' : 'display: none;'); ?>">
            <div class="faq-list">
                <?php $__currentLoopData = $categoryFaqs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $faq): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="faq-item" data-question="<?php echo e(strtolower($faq['question'])); ?>" data-answer="<?php echo e(strtolower($faq['answer'])); ?>">
                    <div class="faq-question" onclick="toggleFaq('<?php echo e($category); ?>', <?php echo e($index); ?>)">
                        <h4><?php echo e($faq['question']); ?></h4>
                        <i class="fas fa-chevron-down faq-icon" id="faqIcon-<?php echo e($category); ?>-<?php echo e($index); ?>"></i>
                    </div>
                    <div class="faq-answer" id="faqAnswer-<?php echo e($category); ?>-<?php echo e($index); ?>">
                        <div class="faq-answer-content">
                            <p><?php echo e($faq['answer']); ?></p>
                            <?php if(isset($faq['links']) && count($faq['links']) > 0): ?>
                            <div class="faq-links">
                                <h5>Link Terkait:</h5>
                                <ul>
                                    <?php $__currentLoopData = $faq['links']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $link): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><a href="<?php echo e($link['url']); ?>" target="_blank"><?php echo e($link['title']); ?></a></li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<!-- Tutorial Videos Section -->
<div class="tutorials-section mb-8" id="tutorials">
    <h2 class="section-title">
        <i class="fas fa-video"></i>
        Tutorial Video
    </h2>
    
    <div class="tutorials-grid">
        <?php $__currentLoopData = $tutorials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tutorial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="tutorial-card">
            <div class="tutorial-thumbnail">
                <img src="<?php echo e($tutorial['thumbnail']); ?>" alt="<?php echo e($tutorial['title']); ?>">
                <div class="play-overlay" onclick="playTutorial('<?php echo e($tutorial['video_url']); ?>', '<?php echo e($tutorial['title']); ?>')">
                    <i class="fas fa-play"></i>
                </div>
                <div class="tutorial-duration"><?php echo e($tutorial['duration']); ?></div>
            </div>
            <div class="tutorial-content">
                <h4><?php echo e($tutorial['title']); ?></h4>
                <p><?php echo e($tutorial['description']); ?></p>
                <div class="tutorial-meta">
                    <span class="tutorial-level level-<?php echo e($tutorial['level']); ?>"><?php echo e(ucfirst($tutorial['level'])); ?></span>
                    <span class="tutorial-views">
                        <i class="fas fa-eye"></i>
                        <?php echo e($tutorial['views']); ?> views
                    </span>
                </div>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<!-- Downloads Section -->
<div class="downloads-section mb-8" id="downloads">
    <h2 class="section-title">
        <i class="fas fa-download"></i>
        Unduhan
    </h2>
    
    <div class="downloads-categories">
        <?php $__currentLoopData = $downloads; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category => $files): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="download-category">
            <h3 class="download-category-title">
                <i class="fas fa-folder"></i>
                <?php echo e($category); ?>

            </h3>
            <div class="download-files">
                <?php $__currentLoopData = $files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="download-item">
                    <div class="download-icon">
                        <i class="<?php echo e($file['icon']); ?>"></i>
                    </div>
                    <div class="download-info">
                        <h5><?php echo e($file['name']); ?></h5>
                        <p><?php echo e($file['description']); ?></p>
                        <div class="download-meta">
                            <span class="file-size"><?php echo e($file['size']); ?></span>
                            <span class="file-type"><?php echo e($file['type']); ?></span>
                            <span class="download-count"><?php echo e($file['downloads']); ?> unduhan</span>
                        </div>
                    </div>
                    <div class="download-action">
                        <a href="<?php echo e($file['url']); ?>" class="btn btn-primary btn-sm" target="_blank" onclick="trackDownload('<?php echo e($file['name']); ?>')">
                            <i class="fas fa-download"></i>
                            Unduh
                        </a>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<!-- Support Team Section -->
<div class="team-section mb-8">
    <h2 class="section-title">
        <i class="fas fa-users"></i>
        Tim Dukungan
    </h2>
    
    <div class="team-grid">
        <?php $__currentLoopData = $team; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="team-card">
            <div class="team-avatar">
                <img src="<?php echo e($member['avatar']); ?>" alt="<?php echo e($member['name']); ?>">
                <div class="status-indicator status-<?php echo e($member['status']); ?>"></div>
            </div>
            <div class="team-info">
                <h4><?php echo e($member['name']); ?></h4>
                <p class="team-role"><?php echo e($member['role']); ?></p>
                <p class="team-specialization"><?php echo e($member['specialization']); ?></p>
                <div class="team-contact">
                    <a href="mailto:<?php echo e($member['email']); ?>" class="contact-link">
                        <i class="fas fa-envelope"></i>
                    </a>
                    <?php if(isset($member['phone'])): ?>
                    <a href="tel:<?php echo e($member['phone']); ?>" class="contact-link">
                        <i class="fas fa-phone"></i>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<!-- Ticket Modal -->
<div class="modal" id="ticketModal">
    <div class="modal-content modal-large">
        <div class="modal-header">
            <h3 class="modal-title">Buat Tiket Bantuan</h3>
            <button class="modal-close" onclick="closeTicketModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <form class="ticket-form" id="ticketForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="ticketName">Nama Lengkap *</label>
                        <input type="text" id="ticketName" name="name" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label for="ticketEmail">Email *</label>
                        <input type="email" id="ticketEmail" name="email" class="form-input" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="ticketCategory">Kategori *</label>
                        <select id="ticketCategory" name="category" class="form-select" required>
                            <option value="">Pilih kategori...</option>
                            <option value="bug">Bug Report</option>
                            <option value="feature">Permintaan Fitur</option>
                            <option value="support">Dukungan Teknis</option>
                            <option value="training">Bantuan Pelatihan</option>
                            <option value="account">Masalah Akun</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="ticketPriority">Prioritas *</label>
                        <select id="ticketPriority" name="priority" class="form-select" required>
                            <option value="low">Rendah</option>
                            <option value="medium" selected>Sedang</option>
                            <option value="high">Tinggi</option>
                            <option value="critical">Kritis</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="ticketSubject">Subjek *</label>
                    <input type="text" id="ticketSubject" name="subject" class="form-input" placeholder="Ringkasan singkat masalah Anda" required>
                </div>
                
                <div class="form-group">
                    <label for="ticketDescription">Deskripsi Detail *</label>
                    <textarea id="ticketDescription" name="description" class="form-textarea" rows="6" placeholder="Jelaskan masalah Anda secara detail, termasuk langkah-langkah yang telah dicoba..." required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="ticketEnvironment">Informasi Lingkungan</label>
                    <textarea id="ticketEnvironment" name="environment" class="form-textarea" rows="3" placeholder="Browser, OS, versi aplikasi, dll. (opsional)"></textarea>
                </div>
                
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="ticketUrgent" name="urgent">
                        <span class="checkmark"></span>
                        Ini adalah masalah mendesak yang memblokir pekerjaan saya
                    </label>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeTicketModal()">Batal</button>
            <button class="btn btn-primary" onclick="submitTicket()">
                <i class="fas fa-ticket-alt"></i>
                Buat Tiket
            </button>
        </div>
    </div>
</div>

<!-- Video Modal -->
<div class="modal" id="videoModal">
    <div class="modal-content modal-video">
        <div class="modal-header">
            <h3 class="modal-title" id="videoModalTitle">Tutorial Video</h3>
            <button class="modal-close" onclick="closeVideoModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="video-container" id="videoContainer">
                <!-- Video will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Map Modal -->
<div class="modal" id="mapModal">
    <div class="modal-content modal-large">
        <div class="modal-header">
            <h3 class="modal-title">Lokasi Kantor</h3>
            <button class="modal-close" onclick="closeMapModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="map-container">
                <iframe 
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3966.521260322283!2d106.8195613!3d-6.1944491!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x2e69f5390917b759%3A0x6b45e67356080477!2sBPS%20Statistics%20Indonesia!5e0!3m2!1sen!2sid!4v1635123456789!5m2!1sen!2sid" 
                    width="100%" 
                    height="400" 
                    style="border:0;" 
                    allowfullscreen="" 
                    loading="lazy" 
                    referrerpolicy="no-referrer-when-downgrade">
                </iframe>
            </div>
            <div class="map-info">
                <h4><?php echo e($contact['office_name']); ?></h4>
                <p><?php echo e($contact['address']); ?></p>
                <div class="map-actions">
                    <a href="https://maps.google.com/?q=<?php echo e(urlencode($contact['address'])); ?>" class="btn btn-primary" target="_blank">
                        <i class="fas fa-directions"></i>
                        Petunjuk Arah
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Page Header */
    .page-header {
        background: linear-gradient(135deg, var(--bg-white) 0%, #f8fafc 100%);
        border-radius: 0.75rem;
        padding: 2rem;
        border: 1px solid var(--border-color);
    }

    .page-header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 2rem;
    }

    .page-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .page-title i {
        color: var(--primary-color);
    }

    .page-subtitle {
        color: var(--text-secondary);
        font-size: 1.125rem;
        line-height: 1.6;
    }

    /* Quick Help Cards */
    .help-card {
        background: var(--bg-white);
        border-radius: 0.75rem;
        padding: 2rem;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
        text-align: center;
        transition: all 0.3s ease;
    }

    .help-card:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
    }

    .help-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin: 0 auto 1rem;
    }

    .help-card h3 {
        color: var(--text-primary);
        margin-bottom: 0.75rem;
        font-size: 1.25rem;
        font-weight: 600;
    }

    .help-card p {
        color: var(--text-secondary);
        margin-bottom: 1.5rem;
        line-height: 1.5;
    }

    /* Contact Section */
    .contact-info,
    .contact-form-section {
        background: var(--bg-white);
        border-radius: 0.75rem;
        padding: 2rem;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid var(--border-color);
    }

    .section-title i {
        color: var(--primary-color);
    }

    /* Contact Methods */
    .contact-methods {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .contact-method {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1.5rem;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 0.75rem;
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .contact-method:hover {
        box-shadow: var(--shadow-sm);
        border-color: var(--primary-color);
    }

    .method-icon {
        width: 50px;
        height: 50px;
        border-radius: 0.5rem;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
        flex-shrink: 0;
    }

    .method-icon .fa-whatsapp {
        color: #25d366;
        background: white;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .method-content {
        flex: 1;
    }

    .method-content h4 {
        color: var(--text-primary);
        margin-bottom: 0.25rem;
        font-size: 1.125rem;
        font-weight: 600;
    }

    .method-content p {
        color: var(--text-primary);
        margin-bottom: 0.25rem;
        font-weight: 500;
    }

    .availability {
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    .method-action {
        flex-shrink: 0;
    }

    /* Form Styles */
    .contact-form,
    .ticket-form {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    .form-group label {
        color: var(--text-primary);
        font-weight: 500;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }

    .form-input,
    .form-select,
    .form-textarea {
        padding: 0.75rem 1rem;
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        background: var(--bg-white);
    }

    .form-input:focus,
    .form-select:focus,
    .form-textarea:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-textarea {
        resize: vertical;
        min-height: 100px;
    }

    .checkbox-label {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        cursor: pointer;
        font-size: 0.875rem;
        color: var(--text-secondary);
    }

    .checkbox-label input[type="checkbox"] {
        width: 18px;
        height: 18px;
        accent-color: var(--primary-color);
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 1rem;
    }

    /* FAQ Section */
    .faq-categories {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        gap: 2rem;
    }

    .faq-tabs {
        display: flex;
        gap: 0.5rem;
    }

    .faq-tab {
        padding: 0.75rem 1.5rem;
        border: 1px solid var(--border-color);
        background: var(--bg-white);
        color: var(--text-secondary);
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .faq-tab.active,
    .faq-tab:hover {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .faq-search {
        flex: 1;
        max-width: 300px;
    }

    .faq-item {
        background: var(--bg-white);
        border: 1px solid var(--border-color);
        border-radius: 0.75rem;
        margin-bottom: 1rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .faq-item:hover {
        box-shadow: var(--shadow-sm);
    }

    .faq-question {
        padding: 1.5rem;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        transition: all 0.3s ease;
    }

    .faq-question:hover {
        background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    }

    .faq-question h4 {
        color: var(--text-primary);
        margin: 0;
        font-size: 1.125rem;
        font-weight: 600;
        flex: 1;
    }

    .faq-icon {
        color: var(--primary-color);
        transition: transform 0.3s ease;
    }

    .faq-icon.rotated {
        transform: rotate(180deg);
    }

    .faq-answer {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }

    .faq-answer.open {
        max-height: 500px;
    }

    .faq-answer-content {
        padding: 1.5rem;
        border-top: 1px solid var(--border-color);
    }

    .faq-answer-content p {
        color: var(--text-secondary);
        line-height: 1.6;
        margin-bottom: 1rem;
    }

    .faq-links h5 {
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        font-weight: 600;
    }

    .faq-links ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .faq-links li {
        margin-bottom: 0.25rem;
    }

    .faq-links a {
        color: var(--primary-color);
        text-decoration: none;
        font-size: 0.875rem;
    }

    .faq-links a:hover {
        text-decoration: underline;
    }

    /* Tutorial Section */
    .tutorials-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .tutorial-card {
        background: var(--bg-white);
        border-radius: 0.75rem;
        overflow: hidden;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
        transition: all 0.3s ease;
    }

    .tutorial-card:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
    }

    .tutorial-thumbnail {
        position: relative;
        aspect-ratio: 16/9;
        overflow: hidden;
    }

    .tutorial-thumbnail img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .play-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        opacity: 0;
    }

    .tutorial-thumbnail:hover .play-overlay {
        opacity: 1;
    }

    .play-overlay i {
        color: white;
        font-size: 3rem;
    }

    .tutorial-duration {
        position: absolute;
        bottom: 0.5rem;
        right: 0.5rem;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .tutorial-content {
        padding: 1.5rem;
    }

    .tutorial-content h4 {
        color: var(--text-primary);
        margin-bottom: 0.75rem;
        font-size: 1.125rem;
        font-weight: 600;
        line-height: 1.4;
    }

    .tutorial-content p {
        color: var(--text-secondary);
        margin-bottom: 1rem;
        line-height: 1.5;
        font-size: 0.875rem;
    }

    .tutorial-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .tutorial-level {
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
        color: white;
    }

    .level-beginner { background: #10b981; }
    .level-intermediate { background: #f59e0b; }
    .level-advanced { background: #ef4444; }

    .tutorial-views {
        color: var(--text-secondary);
        font-size: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    /* Downloads Section */
    .download-category {
        background: var(--bg-white);
        border-radius: 0.75rem;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
        margin-bottom: 2rem;
        overflow: hidden;
    }

    .download-category-title {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        padding: 1.5rem;
        margin: 0;
        color: var(--text-primary);
        font-size: 1.25rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        border-bottom: 1px solid var(--border-color);
    }

    .download-category-title i {
        color: var(--primary-color);
    }

    .download-files {
        padding: 1.5rem;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .download-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 0.5rem;
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .download-item:hover {
        box-shadow: var(--shadow-sm);
        border-color: var(--primary-color);
    }

    .download-icon {
        width: 40px;
        height: 40px;
        border-radius: 0.375rem;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.125rem;
        flex-shrink: 0;
    }

    .download-info {
        flex: 1;
    }

    .download-info h5 {
        color: var(--text-primary);
        margin-bottom: 0.25rem;
        font-size: 1rem;
        font-weight: 600;
    }

    .download-info p {
        color: var(--text-secondary);
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        line-height: 1.4;
    }

    .download-meta {
        display: flex;
        gap: 1rem;
        font-size: 0.75rem;
        color: var(--text-secondary);
    }

    .download-action {
        flex-shrink: 0;
    }

    /* Team Section */
    .team-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1.5rem;
    }

    .team-card {
        background: var(--bg-white);
        border-radius: 0.75rem;
        padding: 1.5rem;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
        text-align: center;
        transition: all 0.3s ease;
    }

    .team-card:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
    }

    .team-avatar {
        position: relative;
        width: 80px;
        height: 80px;
        margin: 0 auto 1rem;
    }

    .team-avatar img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid var(--border-color);
    }

    .status-indicator {
        position: absolute;
        bottom: 5px;
        right: 5px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        border: 2px solid white;
    }

    .status-online { background: #10b981; }
    .status-busy { background: #f59e0b; }
    .status-offline { background: #6b7280; }

    .team-info h4 {
        color: var(--text-primary);
        margin-bottom: 0.25rem;
        font-size: 1.125rem;
        font-weight: 600;
    }

    .team-role {
        color: var(--primary-color);
        font-weight: 500;
        margin-bottom: 0.25rem;
        font-size: 0.875rem;
    }

    .team-specialization {
        color: var(--text-secondary);
        margin-bottom: 1rem;
        font-size: 0.875rem;
        line-height: 1.4;
    }

    .team-contact {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
    }

    .contact-link {
        width: 36px;
        height: 36px;
        border-radius: 0.375rem;
        background: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .contact-link:hover {
        background: var(--primary-dark);
        transform: translateY(-2px);
    }

    /* Modal Styles */
    .modal-video .modal-content {
        max-width: 900px;
    }

    .video-container {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
    }

    .video-container iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: none;
        border-radius: 0.5rem;
    }

    .map-container {
        margin-bottom: 1.5rem;
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .map-info {
        text-align: center;
    }

    .map-info h4 {
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        font-size: 1.25rem;
        font-weight: 600;
    }

    .map-info p {
        color: var(--text-secondary);
        margin-bottom: 1rem;
        line-height: 1.5;
    }

    .map-actions {
        display: flex;
        justify-content: center;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .page-header-content {
            flex-direction: column;
            align-items: stretch;
        }

        .grid-cols-lg-2 {
            grid-template-columns: 1fr;
        }

        .grid-cols-md-3 {
            grid-template-columns: 1fr;
        }

        .faq-categories {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        .faq-tabs {
            flex-wrap: wrap;
        }

        .form-row {
            grid-template-columns: 1fr;
        }

        .form-actions {
            flex-direction: column;
        }

        .contact-method {
            flex-direction: column;
            text-align: center;
        }

        .tutorials-grid {
            grid-template-columns: 1fr;
        }

        .team-grid {
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        }
    }

    @media (max-width: 480px) {
        .download-item {
            flex-direction: column;
            text-align: center;
        }

        .download-meta {
            justify-content: center;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        // FAQ search functionality
        document.getElementById('faqSearch').addEventListener('input', searchFAQ);
        
        // Form validation
        document.getElementById('contactForm').addEventListener('submit', handleContactSubmit);
    });

    // Scroll to section
    function scrollToSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            section.scrollIntoView({ behavior: 'smooth' });
        }
    }

    // FAQ functionality
    function showFaqCategory(category) {
        // Update tabs
        document.querySelectorAll('.faq-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        event.target.classList.add('active');
        
        // Show/hide content
        document.querySelectorAll('.faq-category-content').forEach(content => {
            content.style.display = 'none';
        });
        document.getElementById(`faq-${category}`).style.display = 'block';
        
        // Clear search
        document.getElementById('faqSearch').value = '';
    }

    function toggleFaq(category, index) {
        const answer = document.getElementById(`faqAnswer-${category}-${index}`);
        const icon = document.getElementById(`faqIcon-${category}-${index}`);
        
        answer.classList.toggle('open');
        icon.classList.toggle('rotated');
    }

    function searchFAQ() {
        const searchTerm = document.getElementById('faqSearch').value.toLowerCase();
        const faqItems = document.querySelectorAll('.faq-item');
        
        faqItems.forEach(item => {
            const question = item.dataset.question;
            const answer = item.dataset.answer;
            
            if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = searchTerm ? 'none' : 'block';
            }
        });
    }

    // Contact form handling
    function handleContactSubmit(e) {
        e.preventDefault();
        
        // Get form data
        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData);
        
        // Simulate form submission
        console.log('Contact form submitted:', data);
        
        // Show success message
        alert('Pesan Anda telah terkirim! Kami akan merespons dalam 24 jam.');
        
        // Reset form
        e.target.reset();
    }

    function resetForm() {
        document.getElementById('contactForm').reset();
    }

    // Ticket modal
    function openTicketModal() {
        document.getElementById('ticketModal').style.display = 'block';
    }

    function closeTicketModal() {
        document.getElementById('ticketModal').style.display = 'none';
    }

    function submitTicket() {
        const form = document.getElementById('ticketForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        
        // Validate required fields
        const requiredFields = ['name', 'email', 'category', 'priority', 'subject', 'description'];
        const missingFields = requiredFields.filter(field => !data[field]);
        
        if (missingFields.length > 0) {
            alert('Mohon lengkapi semua field yang wajib diisi.');
            return;
        }
        
        // Generate ticket ID
        const ticketId = 'TKT-' + Date.now().toString().slice(-6);
        
        // Simulate ticket creation
        console.log('Ticket created:', { id: ticketId, ...data });
        
        // Show success message
        alert(`Tiket berhasil dibuat!\nID Tiket: ${ticketId}\n\nAnda akan menerima email konfirmasi segera.`);
        
        // Close modal and reset form
        closeTicketModal();
        form.reset();
    }

    // Video modal
    function playTutorial(videoUrl, title) {
        document.getElementById('videoModalTitle').textContent = title;
        
        // Create iframe for video
        const videoContainer = document.getElementById('videoContainer');
        videoContainer.innerHTML = `
            <iframe 
                src="${videoUrl}" 
                allowfullscreen
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture">
            </iframe>
        `;
        
        document.getElementById('videoModal').style.display = 'block';
    }

    function closeVideoModal() {
        document.getElementById('videoModal').style.display = 'none';
        document.getElementById('videoContainer').innerHTML = '';
    }

    // Map modal
    function showMap() {
        document.getElementById('mapModal').style.display = 'block';
    }

    function closeMapModal() {
        document.getElementById('mapModal').style.display = 'none';
    }

    // Download tracking
    function trackDownload(fileName) {
        console.log('Download tracked:', fileName);
        
        // Update download count in localStorage
        const downloads = JSON.parse(localStorage.getItem('downloadStats') || '{}');
        downloads[fileName] = (downloads[fileName] || 0) + 1;
        localStorage.setItem('downloadStats', JSON.stringify(downloads));
    }

    // Modal close on outside click
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                this.style.display = 'none';
                if (this.id === 'videoModal') {
                    document.getElementById('videoContainer').innerHTML = '';
                }
            }
        });
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            // Close any open modals
            document.querySelectorAll('.modal').forEach(modal => {
                if (modal.style.display === 'block') {
                    modal.style.display = 'none';
                    if (modal.id === 'videoModal') {
                        document.getElementById('videoContainer').innerHTML = '';
                    }
                }
            });
        }
    });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\pengolahanwilker\resources\views/training/contact.blade.php ENDPATH**/ ?>