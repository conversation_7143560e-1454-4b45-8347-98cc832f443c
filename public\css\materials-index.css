/* Materials Index Page Styles */
:root {
    --primary-color: #3b82f6;
    --primary-dark: #1d4ed8;
    --primary-light: #dbeafe;
    --secondary-color: #64748b;
    --accent-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --success-color: #10b981;
    
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    
    --bg-white: #ffffff;
    --bg-light: #f8fafc;
    --bg-gray: #f1f5f9;
    
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* Base Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-light);
}

/* Grid System */
.grid {
    display: grid;
    gap: 1.5rem;
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

@media (min-width: 768px) {
    .grid-cols-md-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .grid-cols-md-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .grid-cols-md-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
    .grid-cols-lg-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
    .grid-cols-lg-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: var(--radius-md);
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    line-height: 1;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    background: linear-gradient(135deg, var(--primary-dark), #1e40af);
}

.btn-secondary {
    background: var(--bg-white);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    background: #5a6268;
    border-color: #545b62;
    color: #fff;
    transform: translateY(-1px);
}

/* Preview button enhanced design */
.btn-outline-primary {
    background-color: #f8f9fa;
    border: 2px solid #007bff;
    color: #007bff;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #059669);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-success:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    background: linear-gradient(135deg, #059669, #047857);
}

/* Spacing Utilities */
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

/* Special Cards */
.featured-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: var(--radius-xl);
    padding: 2.5rem;
    color: white;
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.featured-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.featured-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.featured-card .card-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(10px);
}

.featured-card .card-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: white;
}

.featured-card .card-description {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
}

.featured-card .card-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.featured-card .btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    font-weight: 600;
}

.featured-card .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Enhanced Material Cards */
.enhanced-material-card {
    background: var(--bg-white);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: var(--shadow-sm);
}

.enhanced-material-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-3px);
    border-color: var(--primary-color);
}

.enhanced-material-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.enhanced-card-header {
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--bg-light) 0%, #e2e8f0 100%);
    border-bottom: 1px solid var(--border-color);
}

.enhanced-card-content {
    padding: 1.5rem;
}

.enhanced-card-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    background: var(--bg-light);
}

/* Filter Section Enhancement */
.filter-select {
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-white);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all 0.3s ease;
    min-width: 120px;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Modal Enhancements */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--bg-white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

.modal-large {
    width: 600px;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--bg-light) 0%, #e2e8f0 100%);
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--bg-light);
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    background: var(--bg-light);
}

/* No Results Enhancement */
.no-results {
    text-align: center;
    padding: 4rem 2rem;
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.no-results-content i {
    font-size: 4rem;
    color: var(--text-muted);
    margin-bottom: 1.5rem;
}

.no-results-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.no-results-content p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header-content {
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .page-actions {
        display: flex;
        gap: 1rem;
        width: 100%;
    }
    
    .page-actions .btn {
        flex: 1;
        justify-content: center;
    }
    
    .search-controls {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .module-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .module-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .materials-grid {
        grid-template-columns: 1fr;
    }
    
    .featured-card {
        padding: 2rem;
    }
    
    .featured-card .card-actions {
        flex-direction: column;
    }
    
    .featured-card .btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .overview-card {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
    
    .drive-access-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
    
    .modal-content {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }
}

/* Animation Utilities */
.fade-in {
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Module sections */
.module-section {
    margin-bottom: 2rem;
    border-radius: 12px;
    background: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.module-section:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

/* Collapsed accordion state */
.module-content.collapsed {
    display: none;
}

.module-content {
    display: block;
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}