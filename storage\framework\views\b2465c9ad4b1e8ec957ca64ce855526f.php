<?php $__env->startSection('title', $pendahuluanData['title']); ?>

<?php $__env->startSection('content'); ?>
<div class="enhanced-training-container">
    <!-- Enhanced Page Header with Progress Indicator -->
    <div class="enhanced-page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="header-content">
                        <div class="breadcrumb-enhanced">
                            <a href="<?php echo e(route('home')); ?>">Beranda</a>
                            <i class="fas fa-chevron-right"></i>
                            <a href="<?php echo e(route('materials')); ?>">Materi</a>
                            <i class="fas fa-chevron-right"></i>
                            <span><?php echo e($pendahuluanData['title']); ?></span>
                        </div>
                        <h1 class="enhanced-page-title"><?php echo e($pendahuluanData['title']); ?></h1>
                        <p class="enhanced-page-subtitle"><?php echo e($pendahuluanData['subtitle']); ?></p>
                        <p class="enhanced-page-description"><?php echo e($pendahuluanData['description']); ?></p>
                        
                        <!-- Learning Objectives Preview -->
                        <div class="learning-objectives-preview">
                            <h4><i class="fas fa-target"></i> Tujuan Pembelajaran</h4>
                            <div class="objectives-grid">
                                <div class="objective-item">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Memahami latar belakang Wilkerstat SE2026</span>
                                </div>
                                <div class="objective-item">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Mengenal instrumen dan perangkat</span>
                                </div>
                                <div class="objective-item">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Memahami jadwal kegiatan</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="enhanced-material-info">
                        <div class="info-card">
                            <div class="info-header">
                                <i class="fas fa-info-circle"></i>
                                <h4>Informasi Materi</h4>
                            </div>
                            <div class="info-content">
                                <div class="info-item">
                                    <i class="fas fa-clock"></i>
                                    <span>Durasi: <?php echo e($pendahuluanData['duration']); ?></span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-signal"></i>
                                    <span>Level: <?php echo e($pendahuluanData['difficulty']); ?></span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-users"></i>
                                    <span>Format: Pembelajaran Mandiri</span>
                                </div>
                            </div>
                            <div class="info-actions">
                                <a href="<?php echo e($pendahuluanData['pdf_link']); ?>" class="btn btn-primary" target="_blank">
                                    <i class="fas fa-download"></i> Download PDF
                                </a>
                                <button class="btn btn-secondary" onclick="toggleBookmark()">
                                    <i class="far fa-bookmark"></i> Bookmark
                                </button>
                            </div>
                        </div>
                        
                        <!-- Progress Tracker -->
                        <div class="progress-tracker">
                            <h5><i class="fas fa-chart-line"></i> Progress Pembelajaran</h5>
                            <div class="progress-bar">
                                <div class="progress-fill" id="learningProgress"></div>
                            </div>
                            <span class="progress-text">0% Selesai</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Content with Accordion Sections -->
    <div class="enhanced-content-sections">
        <div class="container">
            <div class="row">
                <!-- Main Content with Accordion -->
                <div class="col-lg-9">
                    <div class="content-accordion">
                        <?php $__currentLoopData = $pendahuluanData['sections']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sectionKey => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="accordion-item" data-section="<?php echo e($sectionKey); ?>">
                            <div class="accordion-header" onclick="toggleAccordion('<?php echo e($sectionKey); ?>')">
                                <div class="header-content">
                                    <div class="header-left">
                                        <i class="<?php echo e($section['icon']); ?> section-icon"></i>
                                        <h3 class="section-title"><?php echo e($section['title']); ?></h3>
                                    </div>
                                    <div class="header-right">
                                        <span class="reading-time"><i class="fas fa-clock"></i> 5 menit</span>
                                        <i class="fas fa-chevron-down accordion-arrow"></i>
                                    </div>
                                </div>
                                <div class="section-preview">
                                    <p><?php echo e(Str::limit(is_array($section['content']) ? $section['content'][0] : $section['content'], 120)); ?></p>
                                </div>
                            </div>
                            
                            <div class="accordion-content" id="content-<?php echo e($sectionKey); ?>">
                                <div class="content-wrapper">
                                    <?php if($sectionKey === 'latar_belakang'): ?>
                                        <!-- Enhanced Latar Belakang Content -->
                                        <div class="content-section-enhanced">
                                            <div class="content-intro">
                                                <div class="intro-card">
                                                    <i class="fas fa-lightbulb"></i>
                                                    <div>
                                                        <h4>Mengapa Penting?</h4>
                                                        <p>Pemahaman latar belakang Wilkerstat SE2026 adalah fondasi untuk memahami seluruh proses pengolahan data.</p>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="content-text-enhanced">
                                                <?php $__currentLoopData = $section['content']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $content): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="content-paragraph" data-paragraph="<?php echo e($index); ?>">
                                                    <div class="paragraph-marker"><?php echo e($index + 1); ?></div>
                                                    <p><?php echo e($content); ?></p>
                                                </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                            
                                            <?php if(isset($section['tahapan'])): ?>
                                            <div class="tahapan-enhanced">
                                                <h4 class="subsection-title">
                                                    <i class="fas fa-list-ol"></i>
                                                    Tahapan Kegiatan
                                                </h4>
                                                <div class="tahapan-timeline">
                                                    <?php $__currentLoopData = $section['tahapan']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tahapanKey => $tahapan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="timeline-item" onclick="expandTimeline('<?php echo e($tahapanKey); ?>')">
                                                        <div class="timeline-marker">
                                                            <i class="<?php echo e($tahapan['icon']); ?>"></i>
                                                        </div>
                                                        <div class="timeline-content">
                                                            <h5><?php echo e($tahapan['title']); ?></h5>
                                                            <div class="timeline-details" id="timeline-<?php echo e($tahapanKey); ?>">
                                                                <ul>
                                                                    <?php $__currentLoopData = $tahapan['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <li><?php echo e($item); ?></li>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                    <?php elseif($sectionKey === 'maksud_tujuan'): ?>
                                        <!-- Enhanced Maksud dan Tujuan Content -->
                                        <div class="objectives-enhanced">
                                            <div class="objectives-intro">
                                                <div class="intro-card">
                                                    <i class="fas fa-bullseye"></i>
                                                    <div>
                                                        <h4>Fokus Pembelajaran</h4>
                                                        <p>Setiap tujuan dirancang untuk membangun pemahaman yang komprehensif dan aplikatif.</p>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="objectives-grid-enhanced">
                                                <?php $__currentLoopData = $section['content']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $objective): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="objective-card" data-objective="<?php echo e($index); ?>">
                                                    <div class="objective-number"><?php echo e($index + 1); ?></div>
                                                    <div class="objective-content">
                                                        <i class="fas fa-check-circle"></i>
                                                        <p><?php echo e($objective); ?></p>
                                                    </div>
                                                    <div class="objective-status">
                                                        <button class="btn-mark-complete" onclick="markObjectiveComplete(<?php echo e($index); ?>)">
                                                            <i class="far fa-square"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                        
                                    <?php elseif($sectionKey === 'instrumen_perangkat'): ?>
                                        <!-- Enhanced Instrumen dan Perangkat Content -->
                                        <div class="instruments-enhanced">
                                            <div class="instruments-tabs">
                                                <div class="tab-buttons">
                                                    <?php if(isset($section['pengolahan_master'])): ?>
                                                    <button class="tab-btn active" onclick="switchTab('master')">
                                                        <i class="<?php echo e($section['pengolahan_master']['icon']); ?>"></i>
                                                        Master
                                                    </button>
                                                    <?php endif; ?>
                                                    <?php if(isset($section['pengolahan_peta'])): ?>
                                                    <button class="tab-btn" onclick="switchTab('peta')">
                                                        <i class="<?php echo e($section['pengolahan_peta']['icon']); ?>"></i>
                                                        Peta
                                                    </button>
                                                    <?php endif; ?>
                                                    <?php if(isset($section['software_aplikasi'])): ?>
                                                    <button class="tab-btn" onclick="switchTab('software')">
                                                        <i class="<?php echo e($section['software_aplikasi']['icon']); ?>"></i>
                                                        Software
                                                    </button>
                                                    <?php endif; ?>
                                                </div>
                                                
                                                <div class="tab-contents">
                                                    <?php if(isset($section['pengolahan_master'])): ?>
                                                    <div class="tab-content active" id="tab-master">
                                                        <h4><?php echo e($section['pengolahan_master']['title']); ?></h4>
                                                        <div class="instrument-grid">
                                                            <?php $__currentLoopData = $section['pengolahan_master']['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="instrument-item">
                                                                <i class="fas fa-file-alt"></i>
                                                                <span><?php echo e($item); ?></span>
                                                            </div>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </div>
                                                    </div>
                                                    <?php endif; ?>
                                                    
                                                    <?php if(isset($section['pengolahan_peta'])): ?>
                                                    <div class="tab-content" id="tab-peta">
                                                        <h4><?php echo e($section['pengolahan_peta']['title']); ?></h4>
                                                        <div class="instrument-grid">
                                                            <?php $__currentLoopData = $section['pengolahan_peta']['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="instrument-item">
                                                                <i class="fas fa-map"></i>
                                                                <span><?php echo e($item); ?></span>
                                                            </div>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </div>
                                                    </div>
                                                    <?php endif; ?>
                                                    
                                                    <?php if(isset($section['software_aplikasi'])): ?>
                                                    <div class="tab-content" id="tab-software">
                                                        <h4><?php echo e($section['software_aplikasi']['title']); ?></h4>
                                                        <div class="instrument-grid">
                                                            <?php $__currentLoopData = $section['software_aplikasi']['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="instrument-item">
                                                                <i class="fas fa-laptop-code"></i>
                                                                <span><?php echo e($item); ?></span>
                                                            </div>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </div>
                                                    </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        
                                    <?php else: ?>
                                        <!-- Default Enhanced Content -->
                                        <div class="content-text-enhanced">
                                            <p><?php echo e($section['content']); ?></p>
                                            <?php if(isset($section['reference_link'])): ?>
                                            <div class="reference-enhanced">
                                                <a href="<?php echo e($section['reference_link']); ?>" class="btn btn-outline-primary">
                                                    <i class="fas fa-external-link-alt"></i>
                                                    <?php echo e($section['reference_text']); ?>

                                                </a>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if(isset($section['kesimpulan'])): ?>
                                    <div class="section-conclusion-enhanced">
                                        <div class="conclusion-card">
                                            <div class="conclusion-header">
                                                <i class="fas fa-lightbulb"></i>
                                                <h5>Kesimpulan</h5>
                                            </div>
                                            <p><?php echo e($section['kesimpulan']); ?></p>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <!-- Section Completion -->
                                    <div class="section-completion">
                                        <button class="btn btn-success btn-complete-section" onclick="completeSection('<?php echo e($sectionKey); ?>')">
                                            <i class="fas fa-check"></i> Tandai Selesai
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    
                    <!-- Navigation Between Sections -->
                    <div class="section-navigation">
                        <button class="btn btn-secondary" onclick="previousSection()" id="prevBtn" disabled>
                            <i class="fas fa-chevron-left"></i> Sebelumnya
                        </button>
                        <button class="btn btn-primary" onclick="nextSection()" id="nextBtn">
                            Selanjutnya <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Enhanced Sidebar -->
                <div class="col-lg-3">
                    <div class="enhanced-sidebar">
                        <!-- Smart Table of Contents -->
                        <div class="smart-toc">
                            <div class="toc-header">
                                <h4><i class="fas fa-list"></i> Daftar Isi</h4>
                                <button class="btn-collapse-all" onclick="collapseAllSections()">
                                    <i class="fas fa-compress-alt"></i>
                                </button>
                            </div>
                            <ul class="toc-list-enhanced">
                                <?php $__currentLoopData = $pendahuluanData['sections']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sectionKey => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="toc-item" data-section="<?php echo e($sectionKey); ?>">
                                    <a href="#<?php echo e($sectionKey); ?>" onclick="scrollToSection('<?php echo e($sectionKey); ?>')">
                                        <div class="toc-content">
                                            <i class="<?php echo e($section['icon']); ?>"></i>
                                            <span><?php echo e($section['title']); ?></span>
                                        </div>
                                        <div class="toc-status">
                                            <i class="fas fa-circle status-indicator"></i>
                                        </div>
                                    </a>
                                </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="quick-actions">
                            <h5><i class="fas fa-bolt"></i> Aksi Cepat</h5>
                            <div class="action-buttons">
                                <button class="action-btn" onclick="printMaterial()">
                                    <i class="fas fa-print"></i>
                                    <span>Cetak</span>
                                </button>
                                <button class="action-btn" onclick="shareMaterial()">
                                    <i class="fas fa-share"></i>
                                    <span>Bagikan</span>
                                </button>
                                <button class="action-btn" onclick="takeNotes()">
                                    <i class="fas fa-sticky-note"></i>
                                    <span>Catatan</span>
                                </button>
                                <button class="action-btn" onclick="toggleDarkMode()">
                                    <i class="fas fa-moon"></i>
                                    <span>Mode Gelap</span>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Related Materials -->
                        <div class="related-materials">
                            <h5><i class="fas fa-link"></i> Materi Terkait</h5>
                            <div class="related-list-enhanced">
                                <a href="<?php echo e(route('organisasi-pengolahan')); ?>" class="related-item-enhanced">
                                    <div class="related-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="related-content">
                                        <h6>Organisasi Pengolahan</h6>
                                        <p>Struktur dan tanggung jawab</p>
                                    </div>
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                                <a href="<?php echo e(route('materi-mekanisme-pen')); ?>" class="related-item-enhanced">
                                    <div class="related-icon">
                                        <i class="fas fa-cogs"></i>
                                    </div>
                                    <div class="related-content">
                                        <h6>Mekanisme Pengolahan</h6>
                                        <p>Prosedur dan workflow</p>
                                    </div>
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                        
                        <!-- Study Notes -->
                        <div class="study-notes" id="studyNotes" style="display: none;">
                            <h5><i class="fas fa-sticky-note"></i> Catatan Anda</h5>
                            <textarea class="notes-textarea" placeholder="Tulis catatan Anda di sini..."></textarea>
                            <div class="notes-actions">
                                <button class="btn btn-sm btn-primary" onclick="saveNotes()">
                                    <i class="fas fa-save"></i> Simpan
                                </button>
                                <button class="btn btn-sm btn-secondary" onclick="clearNotes()">
                                    <i class="fas fa-trash"></i> Hapus
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Enhanced Training Material Styles */
.enhanced-training-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* Enhanced Page Header */
.enhanced-page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    position: relative;
    overflow: hidden;
}

.enhanced-page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
    opacity: 0.3;
}

.header-content {
    position: relative;
    z-index: 2;
}

.breadcrumb-enhanced {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    opacity: 0.9;
}

.breadcrumb-enhanced a {
    color: white;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.breadcrumb-enhanced a:hover {
    opacity: 0.8;
}

.breadcrumb-enhanced i {
    font-size: 0.75rem;
    opacity: 0.7;
}

.enhanced-page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.enhanced-page-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 1rem;
}

.enhanced-page-description {
    font-size: 1rem;
    opacity: 0.8;
    line-height: 1.6;
    margin-bottom: 2rem;
}

/* Learning Objectives Preview */
.learning-objectives-preview {
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.learning-objectives-preview h4 {
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.objectives-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem;
}

.objective-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.objective-item i {
    color: #10b981;
    flex-shrink: 0;
}

/* Enhanced Material Info */
.enhanced-material-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.info-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--text-primary);
}

.info-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);
}

.info-header i {
    color: var(--primary-color);
}

.info-header h4 {
    margin: 0;
    font-size: 1.1rem;
}

.info-content {
    margin-bottom: 1.5rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

.info-item i {
    color: var(--primary-color);
    width: 16px;
    text-align: center;
}

.info-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.info-actions .btn {
    flex: 1;
    min-width: 120px;
}

/* Progress Tracker */
.progress-tracker {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--text-primary);
}

.progress-tracker h5 {
    margin-bottom: 1rem;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.progress-tracker i {
    color: var(--primary-color);
}

.progress-bar {
    background: #e5e7eb;
    border-radius: 10px;
    height: 8px;
    margin-bottom: 0.5rem;
    overflow: hidden;
}

.progress-fill {
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    height: 100%;
    width: 0%;
    transition: width 0.5s ease;
    border-radius: 10px;
}

.progress-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Enhanced Content Sections */
.enhanced-content-sections {
    padding: 3rem 0;
}

/* Accordion Styles */
.content-accordion {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.accordion-item {
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.accordion-item:last-child {
    border-bottom: none;
}

.accordion-item.active {
    background: #f8fafc;
}

.accordion-header {
    padding: 1.5rem 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.accordion-header:hover {
    background: #f8fafc;
    border-left-color: var(--primary-color);
}

.accordion-item.active .accordion-header {
    background: #f0f4ff;
    border-left-color: var(--primary-color);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.section-icon {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.section-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.reading-time {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.accordion-arrow {
    color: var(--text-secondary);
    transition: transform 0.3s ease;
}

.accordion-item.active .accordion-arrow {
    transform: rotate(180deg);
}

.section-preview {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
}

.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.accordion-item.active .accordion-content {
    max-height: 5000px;
}

.content-wrapper {
    padding: 0 2rem 2rem 2rem;
}

/* Enhanced Content Styles */
.content-section-enhanced {
    margin-bottom: 2rem;
}

.content-intro {
    margin-bottom: 2rem;
}

.intro-card {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    padding: 1.5rem;
    border-radius: 12px;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    border: 1px solid #f59e0b;
}

.intro-card i {
    color: #d97706;
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.intro-card h4 {
    margin: 0 0 0.5rem 0;
    color: #92400e;
    font-size: 1.1rem;
}

.intro-card p {
    margin: 0;
    color: #a16207;
    font-size: 0.9rem;
    line-height: 1.5;
}

.content-text-enhanced {
    margin-bottom: 2rem;
}

.content-paragraph {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
    transition: all 0.3s ease;
}

.content-paragraph:hover {
    background: #f0f4ff;
    transform: translateX(4px);
}

.paragraph-marker {
    background: var(--primary-color);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 600;
    flex-shrink: 0;
    margin-top: 0.125rem;
}

.content-paragraph p {
    margin: 0;
    line-height: 1.6;
    color: var(--text-primary);
}

/* Timeline Styles */
.tahapan-enhanced {
    margin-bottom: 2rem;
}

.subsection-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.subsection-title i {
    color: var(--primary-color);
}

.tahapan-timeline {
    position: relative;
    padding-left: 2rem;
}

.tahapan-timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, var(--primary-color), var(--accent-color));
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
    cursor: pointer;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    top: 0.5rem;
    width: 2rem;
    height: 2rem;
    background: white;
    border: 3px solid var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.timeline-marker i {
    color: var(--primary-color);
    font-size: 0.875rem;
}

.timeline-content {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.timeline-item:hover .timeline-content {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.timeline-content h5 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.timeline-details {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.timeline-item.expanded .timeline-details {
    max-height: 500px;
}

.timeline-details ul {
    margin: 0;
    padding-left: 1.5rem;
}

.timeline-details li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* Enhanced Objectives */
.objectives-enhanced {
    margin-bottom: 2rem;
}

.objectives-intro {
    margin-bottom: 2rem;
}

.objectives-grid-enhanced {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.objective-card {
    background: white;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.objective-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.objective-card.completed {
    border-color: var(--accent-color);
    background: #f0fdf4;
}

.objective-number {
    position: absolute;
    top: -12px;
    left: 1.5rem;
    background: var(--primary-color);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 600;
}

.objective-content {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.objective-content i {
    color: var(--primary-color);
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.objective-content p {
    margin: 0;
    line-height: 1.6;
    color: var(--text-primary);
}

.objective-status {
    text-align: right;
}

.btn-mark-complete {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.btn-mark-complete:hover {
    color: var(--accent-color);
}

.objective-card.completed .btn-mark-complete {
    color: var(--accent-color);
}

/* Enhanced Instruments */
.instruments-enhanced {
    margin-bottom: 2rem;
}

.instruments-tabs {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.tab-buttons {
    display: flex;
    background: #f8fafc;
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    flex: 1;
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-weight: 500;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background: #f0f4ff;
    color: var(--primary-color);
}

.tab-btn.active {
    background: white;
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-contents {
    position: relative;
}

.tab-content {
    padding: 2rem;
    display: none;
}

.tab-content.active {
    display: block;
}

.tab-content h4 {
    margin: 0 0 1.5rem 0;
    color: var(--text-primary);
    font-size: 1.2rem;
}

.instrument-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.instrument-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.instrument-item:hover {
    background: #f0f4ff;
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.instrument-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.instrument-item span {
    color: var(--text-primary);
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Section Conclusion */
.section-conclusion-enhanced {
    margin-top: 2rem;
}

.conclusion-card {
    background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
    border: 1px solid #0288d1;
    border-radius: 12px;
    padding: 1.5rem;
}

.conclusion-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.conclusion-header i {
    color: #0277bd;
    font-size: 1.2rem;
}

.conclusion-header h5 {
    margin: 0;
    color: #01579b;
    font-size: 1.1rem;
}

.conclusion-card p {
    margin: 0;
    color: #0277bd;
    line-height: 1.6;
}

/* Section Completion */
.section-completion {
    margin-top: 2rem;
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.btn-complete-section {
    padding: 0.75rem 2rem;
    font-size: 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-complete-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-complete-section.completed {
    background: var(--accent-color);
    border-color: var(--accent-color);
}

/* Section Navigation */
.section-navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 3rem;
    padding: 1.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section-navigation .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 500;
}

/* Enhanced Sidebar */
.enhanced-sidebar {
    position: sticky;
    top: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Smart Table of Contents */
.smart-toc {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.toc-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.toc-header h4 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toc-header i {
    color: var(--primary-color);
}

.btn-collapse-all {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.btn-collapse-all:hover {
    background: #f3f4f6;
    color: var(--primary-color);
}

.toc-list-enhanced {
    list-style: none;
    padding: 0;
    margin: 0;
}

.toc-item {
    margin-bottom: 0.5rem;
}

.toc-item a {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-radius: 8px;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.toc-item a:hover {
    background: #f8fafc;
    border-color: var(--border-color);
}

.toc-item.active a {
    background: #f0f4ff;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.toc-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.toc-content i {
    font-size: 0.875rem;
    width: 16px;
    text-align: center;
}

.toc-content span {
    font-size: 0.9rem;
    font-weight: 500;
}

.toc-status {
    position: relative;
}

.status-indicator {
    font-size: 0.5rem;
    color: #d1d5db;
    transition: color 0.3s ease;
}

.toc-item.completed .status-indicator {
    color: var(--accent-color);
}

.toc-item.current .status-indicator {
    color: var(--primary-color);
}

/* Quick Actions */
.quick-actions {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.quick-actions h5 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quick-actions i {
    color: var(--primary-color);
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 0.75rem;
    background: #f8fafc;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    color: var(--text-primary);
}

.action-btn:hover {
    background: #f0f4ff;
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
}

.action-btn i {
    font-size: 1.2rem;
}

.action-btn span {
    font-size: 0.8rem;
    font-weight: 500;
}

/* Related Materials */
.related-materials {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.related-materials h5 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.related-materials i {
    color: var(--primary-color);
}

.related-list-enhanced {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.related-item-enhanced {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.related-item-enhanced:hover {
    background: #f0f4ff;
    border-color: var(--primary-color);
    transform: translateX(4px);
}

.related-icon {
    background: var(--primary-color);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.related-content {
    flex: 1;
}

.related-content h6 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
}

.related-content p {
    margin: 0;
    font-size: 0.8rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.related-item-enhanced > i:last-child {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Study Notes */
.study-notes {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.study-notes h5 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.study-notes i {
    color: var(--primary-color);
}

.notes-textarea {
    width: 100%;
    min-height: 120px;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-family: inherit;
    font-size: 0.9rem;
    line-height: 1.5;
    resize: vertical;
    margin-bottom: 1rem;
}

.notes-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.notes-actions {
    display: flex;
    gap: 0.5rem;
}

.notes-actions .btn {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .enhanced-page-header {
        padding: 2rem 0;
    }
    
    .enhanced-page-title {
        font-size: 2rem;
    }
    
    .enhanced-page-subtitle {
        font-size: 1.1rem;
    }
    
    .objectives-grid {
        grid-template-columns: 1fr;
    }
    
    .enhanced-material-info {
        margin-top: 2rem;
    }
    
    .info-actions {
        flex-direction: column;
    }
    
    .info-actions .btn {
        min-width: auto;
    }
    
    .enhanced-content-sections {
        padding: 2rem 0;
    }
    
    .accordion-header {
        padding: 1rem 1.5rem;
    }
    
    .content-wrapper {
        padding: 0 1.5rem 1.5rem 1.5rem;
    }
    
    .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .header-right {
        align-self: flex-end;
    }
    
    .objectives-grid-enhanced {
        grid-template-columns: 1fr;
    }
    
    .instrument-grid {
        grid-template-columns: 1fr;
    }
    
    .tab-buttons {
        flex-direction: column;
    }
    
    .section-navigation {
        flex-direction: column;
        gap: 1rem;
    }
    
    .enhanced-sidebar {
        margin-top: 2rem;
        position: static;
    }
    
    .action-buttons {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .action-btn {
        padding: 0.75rem 0.5rem;
    }
    
    .action-btn span {
        font-size: 0.7rem;
    }
}

@media (max-width: 480px) {
    .enhanced-page-title {
        font-size: 1.75rem;
    }
    
    .learning-objectives-preview {
        padding: 1rem;
    }
    
    .objectives-grid {
        gap: 0.5rem;
    }
    
    .objective-item {
        font-size: 0.8rem;
    }
    
    .accordion-header {
        padding: 1rem;
    }
    
    .content-wrapper {
        padding: 0 1rem 1rem 1rem;
    }
    
    .content-paragraph {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .paragraph-marker {
        align-self: flex-start;
    }
    
    .tahapan-timeline {
        padding-left: 1.5rem;
    }
    
    .timeline-marker {
        left: -1.5rem;
        width: 1.5rem;
        height: 1.5rem;
    }
    
    .action-buttons {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Dark Mode Support */
.dark-mode {
    --bg-light: #1f2937;
    --bg-white: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --border-color: #4b5563;
}

.dark-mode .enhanced-training-container {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

.dark-mode .info-card,
.dark-mode .progress-tracker,
.dark-mode .content-accordion,
.dark-mode .smart-toc,
.dark-mode .quick-actions,
.dark-mode .related-materials,
.dark-mode .study-notes {
    background: var(--bg-white);
    border-color: var(--border-color);
}

.dark-mode .timeline-content,
.dark-mode .objective-card {
    background: var(--bg-white);
    border-color: var(--border-color);
}

.dark-mode .intro-card {
    background: linear-gradient(135deg, #451a03 0%, #78350f 100%);
    border-color: #92400e;
}

.dark-mode .conclusion-card {
    background: linear-gradient(135deg, #0c4a6e 0%, #075985 100%);
    border-color: #0369a1;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Enhanced Training Material JavaScript
class EnhancedTrainingMaterial {
    constructor() {
        this.currentSection = 0;
        this.completedSections = new Set();
        this.completedObjectives = new Set();
        this.totalSections = document.querySelectorAll('.accordion-item').length;
        this.isBookmarked = false;
        this.isDarkMode = false;
        
        this.init();
    }
    
    init() {
        this.loadProgress();
        this.updateProgress();
        this.setupEventListeners();
        this.setupIntersectionObserver();
        this.loadNotes();
        this.setupKeyboardShortcuts();
    }
    
    setupEventListeners() {
        // Auto-expand first section
        const firstSection = document.querySelector('.accordion-item');
        if (firstSection) {
            this.toggleAccordion(firstSection.dataset.section);
        }
    }
    
    setupIntersectionObserver() {
        const options = {
            threshold: 0.5,
            rootMargin: '-100px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const section = entry.target.dataset.section;
                    this.updateCurrentSection(section);
                }
            });
        }, options);
        
        document.querySelectorAll('.accordion-item').forEach(item => {
            observer.observe(item);
        });
    }
    
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'ArrowRight':
                        e.preventDefault();
                        this.nextSection();
                        break;
                    case 'ArrowLeft':
                        e.preventDefault();
                        this.previousSection();
                        break;
                    case 'b':
                        e.preventDefault();
                        this.toggleBookmark();
                        break;
                    case 'd':
                        e.preventDefault();
                        this.toggleDarkMode();
                        break;
                }
            }
        });
    }
    
    toggleAccordion(sectionKey) {
        const item = document.querySelector(`[data-section="${sectionKey}"]`);
        const content = document.getElementById(`content-${sectionKey}`);
        
        if (!item || !content) return;
        
        const isActive = item.classList.contains('active');
        
        if (isActive) {
            item.classList.remove('active');
            content.style.maxHeight = '0';
        } else {
            // Close other sections
            document.querySelectorAll('.accordion-item.active').forEach(activeItem => {
                if (activeItem !== item) {
                    activeItem.classList.remove('active');
                    const activeContent = activeItem.querySelector('.accordion-content');
                    if (activeContent) activeContent.style.maxHeight = '0';
                }
            });
            
            item.classList.add('active');
            content.style.maxHeight = content.scrollHeight + 'px';
            
            // Smooth scroll to section
            setTimeout(() => {
                item.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }, 100);
        }
        
        this.updateProgress();
    }
    
    expandTimeline(timelineKey) {
        const item = document.querySelector(`#timeline-${timelineKey}`).closest('.timeline-item');
        item.classList.toggle('expanded');
    }
    
    switchTab(tabKey) {
        // Remove active class from all tabs and contents
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
        
        // Add active class to selected tab and content
        document.querySelector(`[onclick="switchTab('${tabKey}')"]`).classList.add('active');
        document.getElementById(`tab-${tabKey}`).classList.add('active');
    }
    
    markObjectiveComplete(index) {
        const objectiveCard = document.querySelector(`[data-objective="${index}"]`);
        const button = objectiveCard.querySelector('.btn-mark-complete i');
        
        if (this.completedObjectives.has(index)) {
            this.completedObjectives.delete(index);
            objectiveCard.classList.remove('completed');
            button.className = 'far fa-square';
        } else {
            this.completedObjectives.add(index);
            objectiveCard.classList.add('completed');
            button.className = 'fas fa-check-square';
        }
        
        this.saveProgress();
        this.updateProgress();
    }
    
    completeSection(sectionKey) {
        const button = document.querySelector(`[data-section="${sectionKey}"] .btn-complete-section`);
        
        if (this.completedSections.has(sectionKey)) {
            this.completedSections.delete(sectionKey);
            button.classList.remove('completed');
            button.innerHTML = '<i class="fas fa-check"></i> Tandai Selesai';
        } else {
            this.completedSections.add(sectionKey);
            button.classList.add('completed');
            button.innerHTML = '<i class="fas fa-check-circle"></i> Selesai';
        }
        
        this.updateTocStatus(sectionKey);
        this.saveProgress();
        this.updateProgress();
    }
    
    updateTocStatus(sectionKey) {
        const tocItem = document.querySelector(`.toc-item[data-section="${sectionKey}"]`);
        if (this.completedSections.has(sectionKey)) {
            tocItem.classList.add('completed');
        } else {
            tocItem.classList.remove('completed');
        }
    }
    
    updateCurrentSection(sectionKey) {
        document.querySelectorAll('.toc-item').forEach(item => item.classList.remove('current'));
        const currentTocItem = document.querySelector(`.toc-item[data-section="${sectionKey}"]`);
        if (currentTocItem) {
            currentTocItem.classList.add('current');
        }
    }
    
    scrollToSection(sectionKey) {
        const section = document.querySelector(`[data-section="${sectionKey}"]`);
        if (section) {
            section.scrollIntoView({ behavior: 'smooth', block: 'start' });
            if (!section.classList.contains('active')) {
                this.toggleAccordion(sectionKey);
            }
        }
    }
    
    collapseAllSections() {
        document.querySelectorAll('.accordion-item.active').forEach(item => {
            item.classList.remove('active');
            const content = item.querySelector('.accordion-content');
            if (content) content.style.maxHeight = '0';
        });
    }
    
    nextSection() {
        const sections = Array.from(document.querySelectorAll('.accordion-item'));
        const currentIndex = sections.findIndex(section => section.classList.contains('active'));
        
        if (currentIndex < sections.length - 1) {
            const nextSection = sections[currentIndex + 1];
            this.toggleAccordion(nextSection.dataset.section);
        }
        
        this.updateNavigationButtons();
    }
    
    previousSection() {
        const sections = Array.from(document.querySelectorAll('.accordion-item'));
        const currentIndex = sections.findIndex(section => section.classList.contains('active'));
        
        if (currentIndex > 0) {
            const prevSection = sections[currentIndex - 1];
            this.toggleAccordion(prevSection.dataset.section);
        }
        
        this.updateNavigationButtons();
    }
    
    updateNavigationButtons() {
        const sections = Array.from(document.querySelectorAll('.accordion-item'));
        const currentIndex = sections.findIndex(section => section.classList.contains('active'));
        
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        
        if (prevBtn) prevBtn.disabled = currentIndex <= 0;
        if (nextBtn) nextBtn.disabled = currentIndex >= sections.length - 1;
    }
    
    updateProgress() {
        const totalSections = this.totalSections;
        const completedCount = this.completedSections.size;
        const progressPercentage = totalSections > 0 ? (completedCount / totalSections) * 100 : 0;
        
        const progressFill = document.getElementById('learningProgress');
        const progressText = document.querySelector('.progress-text');
        
        if (progressFill) {
            progressFill.style.width = `${progressPercentage}%`;
        }
        
        if (progressText) {
            progressText.textContent = `${Math.round(progressPercentage)}% Selesai`;
        }
    }
    
    toggleBookmark() {
        this.isBookmarked = !this.isBookmarked;
        const bookmarkBtn = document.querySelector('[onclick="toggleBookmark()"]');
        
        if (bookmarkBtn) {
            const icon = bookmarkBtn.querySelector('i');
            if (this.isBookmarked) {
                icon.className = 'fas fa-bookmark';
                bookmarkBtn.innerHTML = '<i class="fas fa-bookmark"></i> Tersimpan';
            } else {
                icon.className = 'far fa-bookmark';
                bookmarkBtn.innerHTML = '<i class="far fa-bookmark"></i> Bookmark';
            }
        }
        
        this.saveProgress();
    }
    
    toggleDarkMode() {
        this.isDarkMode = !this.isDarkMode;
        document.body.classList.toggle('dark-mode', this.isDarkMode);
        
        const darkModeBtn = document.querySelector('[onclick="toggleDarkMode()"]');
        if (darkModeBtn) {
            const icon = darkModeBtn.querySelector('i');
            const span = darkModeBtn.querySelector('span');
            
            if (this.isDarkMode) {
                icon.className = 'fas fa-sun';
                span.textContent = 'Mode Terang';
            } else {
                icon.className = 'fas fa-moon';
                span.textContent = 'Mode Gelap';
            }
        }
        
        localStorage.setItem('darkMode', this.isDarkMode);
    }
    
    printMaterial() {
        window.print();
    }
    
    shareMaterial() {
        if (navigator.share) {
            navigator.share({
                title: document.title,
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(window.location.href).then(() => {
                alert('Link berhasil disalin ke clipboard!');
            });
        }
    }
    
    takeNotes() {
        const notesSection = document.getElementById('studyNotes');
        if (notesSection.style.display === 'none' || !notesSection.style.display) {
            notesSection.style.display = 'block';
            notesSection.scrollIntoView({ behavior: 'smooth' });
        } else {
            notesSection.style.display = 'none';
        }
    }
    
    saveNotes() {
        const textarea = document.querySelector('.notes-textarea');
        if (textarea) {
            localStorage.setItem('trainingNotes', textarea.value);
            alert('Catatan berhasil disimpan!');
        }
    }
    
    loadNotes() {
        const textarea = document.querySelector('.notes-textarea');
        const savedNotes = localStorage.getItem('trainingNotes');
        if (textarea && savedNotes) {
            textarea.value = savedNotes;
        }
    }
    
    clearNotes() {
        const textarea = document.querySelector('.notes-textarea');
        if (textarea && confirm('Yakin ingin menghapus semua catatan?')) {
            textarea.value = '';
            localStorage.removeItem('trainingNotes');
        }
    }
    
    saveProgress() {
        const progress = {
            completedSections: Array.from(this.completedSections),
            completedObjectives: Array.from(this.completedObjectives),
            isBookmarked: this.isBookmarked
        };
        localStorage.setItem('trainingProgress', JSON.stringify(progress));
    }
    
    loadProgress() {
        const saved = localStorage.getItem('trainingProgress');
        if (saved) {
            const progress = JSON.parse(saved);
            this.completedSections = new Set(progress.completedSections || []);
            this.completedObjectives = new Set(progress.completedObjectives || []);
            this.isBookmarked = progress.isBookmarked || false;
        }
        
        const savedDarkMode = localStorage.getItem('darkMode');
        if (savedDarkMode === 'true') {
            this.toggleDarkMode();
        }
    }
}

// Global functions for onclick handlers
function toggleAccordion(sectionKey) {
    window.trainingMaterial.toggleAccordion(sectionKey);
}

function expandTimeline(timelineKey) {
    window.trainingMaterial.expandTimeline(timelineKey);
}

function switchTab(tabKey) {
    window.trainingMaterial.switchTab(tabKey);
}

function markObjectiveComplete(index) {
    window.trainingMaterial.markObjectiveComplete(index);
}

function completeSection(sectionKey) {
    window.trainingMaterial.completeSection(sectionKey);
}

function scrollToSection(sectionKey) {
    window.trainingMaterial.scrollToSection(sectionKey);
}

function collapseAllSections() {
    window.trainingMaterial.collapseAllSections();
}

function nextSection() {
    window.trainingMaterial.nextSection();
}

function previousSection() {
    window.trainingMaterial.previousSection();
}

function toggleBookmark() {
    window.trainingMaterial.toggleBookmark();
}

function toggleDarkMode() {
    window.trainingMaterial.toggleDarkMode();
}

function printMaterial() {
    window.trainingMaterial.printMaterial();
}

function shareMaterial() {
    window.trainingMaterial.shareMaterial();
}

function takeNotes() {
    window.trainingMaterial.takeNotes();
}

function saveNotes() {
    window.trainingMaterial.saveNotes();
}

function clearNotes() {
    window.trainingMaterial.clearNotes();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.trainingMaterial = new EnhancedTrainingMaterial();
    
    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.accordion-item, .info-card, .progress-tracker');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\pengolahanwilker\resources\views/training/enhanced-pendahuluan.blade.php ENDPATH**/ ?>