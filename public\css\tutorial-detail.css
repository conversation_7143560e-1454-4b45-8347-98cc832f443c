/* Tutorial Detail Page Styles */
body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    background-attachment: fixed;
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(66, 153, 225, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(56, 178, 172, 0.08) 0%, transparent 50%);
    z-index: -1;
    pointer-events: none;
}

.tutorial-container {
    background: transparent;
    min-height: 100vh;
    padding: 2rem 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    position: relative;
    z-index: 1;
}

.tutorial-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.tutorial-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    text-align: center;
}

.tutorial-subtitle {
    font-size: 1.25rem;
    color: #4a5568;
    text-align: center;
    margin-bottom: 1rem;
}

.tutorial-description {
    font-size: 1.1rem;
    color: #718096;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.section-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    margin-bottom: 2rem;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.5);
    position: relative;
}

.section-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
}

.section-header {
    background: linear-gradient(135deg, #4299e1 0%, #38b2ac 100%);
    color: white;
    padding: 1.5rem 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.section-header:hover {
    background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.section-icon {
    font-size: 1.75rem;
    opacity: 0.9;
}

.section-content {
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: all 0.5s ease;
}

.section-content.active {
    max-height: 5000px;
    padding: 2rem;
}

.step-item {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f7fafc;
    border-radius: 12px;
    border-left: 4px solid #4299e1;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.step-item:hover {
    background: #edf2f7;
    transform: translateX(5px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.step-number {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.1rem;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
}

.step-content {
    flex: 1;
}

.step-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.step-description {
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.step-note {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 8px;
    padding: 0.75rem;
    color: #c53030;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.step-note::before {
    content: "💡 ";
    font-weight: bold;
}

.step-image {
    margin: 1rem 0;
    text-align: center;
}

.step-image img {
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    max-height: 400px;
    object-fit: contain;
}

.step-image img:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Enhanced formatting styles */
.step-description ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.step-description li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.step-description a {
    font-weight: 600;
    transition: all 0.3s ease;
}

.step-description a:hover {
    color: #2c7a7b !important;
    text-decoration: underline !important;
}

.step-description p {
    margin-bottom: 0.75rem;
    line-height: 1.6;
}

.step-description strong {
    color: #2d3748;
    font-weight: 600;
}

.collapse-icon {
    transition: transform 0.3s ease;
    margin-left: auto;
}

.collapse-icon.rotated {
    transform: rotate(180deg);
}

.back-button {
    background: linear-gradient(135deg, #4299e1 0%, #38b2ac 100%);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
}

.back-button:hover {
    background: linear-gradient(135deg, #3182ce 0%, #319795 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(66, 153, 225, 0.4);
    color: white;
    text-decoration: none;
}

.progress-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    z-index: 1000;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #4299e1 0%, #38b2ac 100%);
    width: 0%;
    transition: width 0.5s ease;
    box-shadow: 0 2px 4px rgba(66, 153, 225, 0.3);
}

/* Subsection styles for 3-level hierarchy */
.subsection-item {
    margin-bottom: 2rem;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    overflow: hidden;
}

.subsection-header {
    background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
    color: white;
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.subsection-content {
    padding: 1.5rem;
    background: #fafafa;
}

/* Nested step items within subsections */
.nested-step-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #ffffff;
    border-radius: 8px;
    border-left: 3px solid #38b2ac;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
    transition: all 0.3s ease;
    margin-left: 1rem;
}

.nested-step-item:hover {
    background: #f7fafc;
    transform: translateX(3px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.06);
}

.nested-step-number {
    background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(56, 178, 172, 0.3);
}

.nested-step-content {
    flex: 1;
}

.nested-step-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.4rem;
}

.nested-step-description {
    color: #4a5568;
    line-height: 1.5;
    margin-bottom: 0.4rem;
    font-size: 0.95rem;
}

.nested-step-note {
    background: #f0fff4;
    border: 1px solid #9ae6b4;
    border-radius: 6px;
    padding: 0.6rem;
    color: #22543d;
    font-size: 0.85rem;
    margin-top: 0.4rem;
}

.nested-step-note::before {
    content: "💡 ";
    font-weight: bold;
}

.nested-step-image {
    margin: 0.8rem 0;
    text-align: center;
}

.nested-step-image img {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    max-height: 300px;
    object-fit: contain;
}

.nested-step-image img:hover {
    transform: scale(1.01);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
}

@media (max-width: 768px) {
    .tutorial-title {
        font-size: 2rem;
    }
    
    .section-header {
        padding: 1rem 1.5rem;
    }
    
    .section-content.active {
        padding: 1.5rem;
    }
    
    .step-item {
        flex-direction: column;
        gap: 1rem;
    }
    
    .step-number {
        align-self: flex-start;
    }
    
    .subsection-content {
        padding: 1rem;
    }
    
    .nested-step-item {
        flex-direction: column;
        gap: 0.8rem;
        margin-left: 0.5rem;
    }
    
    .nested-step-number {
        align-self: flex-start;
    }
}